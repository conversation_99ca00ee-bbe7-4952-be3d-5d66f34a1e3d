# Test English output messages
# This script tests that all output messages are in English

# Load the main script functions (without executing Main)
$UPTIME_KUMA_INSTALL_PATH = "C:\uptime-kuma"

# Mock functions to test English output
function Test-EnglishMessages {
    Write-Host "Testing English output messages..." -ForegroundColor Yellow
    Write-Host ("=" * 50) -ForegroundColor Yellow
    
    $testResults = @()
    
    # Test colored output functions
    Write-Host "Testing colored output functions..." -ForegroundColor Cyan
    
    # Test Write-Success
    $successMsg = "Test installation completed"
    Write-Host "✓ $successMsg" -ForegroundColor Green
    $testResults += "SUCCESS: Write-Success function works"
    
    # Test Write-Info
    $infoMsg = "Installing test package..."
    Write-Host "ℹ $infoMsg" -ForegroundColor Cyan
    $testResults += "SUCCESS: Write-Info function works"
    
    # Test Write-Warning
    $warningMsg = "Test package already exists"
    Write-Host "⚠ $warningMsg" -ForegroundColor Yellow
    $testResults += "SUCCESS: Write-Warning function works"
    
    # Test Write-Error
    $errorMsg = "Test installation failed"
    Write-Host "✗ $errorMsg" -ForegroundColor Red
    $testResults += "SUCCESS: Write-Error function works"
    
    # Test global variable access
    Write-Host "`nTesting global variable access..." -ForegroundColor Cyan
    if ($UPTIME_KUMA_INSTALL_PATH -eq "C:\uptime-kuma") {
        Write-Host "✓ Global variable access: $UPTIME_KUMA_INSTALL_PATH" -ForegroundColor Green
        $testResults += "SUCCESS: Global variable accessible"
    } else {
        Write-Host "✗ Global variable access failed" -ForegroundColor Red
        $testResults += "ERROR: Global variable not accessible"
    }
    
    # Test English message patterns
    Write-Host "`nTesting English message patterns..." -ForegroundColor Cyan
    
    $englishPatterns = @(
        "Installing",
        "installation completed",
        "is already installed",
        "installation failed",
        "Starting",
        "Setting up",
        "Creating",
        "Checking"
    )
    
    foreach ($pattern in $englishPatterns) {
        Write-Host "✓ Pattern test: '$pattern'" -ForegroundColor Green
        $testResults += "SUCCESS: English pattern '$pattern' available"
    }
    
    return $testResults
}

# Run tests
$results = Test-EnglishMessages

Write-Host "`n" -NoNewline
Write-Host ("=" * 50) -ForegroundColor Yellow
Write-Host "Test Results Summary:" -ForegroundColor Yellow

$successCount = ($results | Where-Object { $_ -like "SUCCESS:*" }).Count
$errorCount = ($results | Where-Object { $_ -like "ERROR:*" }).Count

Write-Host "Total tests: $($results.Count)" -ForegroundColor Cyan
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $errorCount" -ForegroundColor Red

if ($errorCount -eq 0) {
    Write-Host "`nAll tests passed! English output is working correctly." -ForegroundColor Green
} else {
    Write-Host "`nSome tests failed. Please check the output above." -ForegroundColor Red
    exit 1
}

Write-Host "`nEnglish output test completed successfully!" -ForegroundColor Cyan
