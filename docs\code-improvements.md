# 代碼改進記錄

## 2025-07-23: 統一 Uptime Kuma 安裝路徑定義

### 問題描述
在 PowerShell 腳本 `Install-DevTools.ps1` 中，Uptime Kuma 的安裝路徑在多個地方重複定義：
1. `Install-UptimeKuma` 函數中的 `$installPath = "C:\uptime-kuma"`
2. 主程序 `Main` 函數中的 `$uptimeKumaPath = "C:\uptime-kuma"`
3. `Show-InstallationSummary` 函數中的硬編碼路徑

### 問題影響
- **維護困難**：需要修改路徑時必須記得同時修改多個地方
- **容易出錯**：可能遺漏某個地方的修改，導致路徑不一致
- **代碼重複**：違反 DRY (Don't Repeat Yourself) 原則

### 解決方案
將安裝路徑定義為全域常數，統一管理：

```powershell
# 全域常數定義
$UPTIME_KUMA_INSTALL_PATH = "C:\uptime-kuma"
```

### 修改內容

#### 1. 新增全域常數定義
在腳本開頭（第 17-18 行）新增：
```powershell
# 全域常數定義
$UPTIME_KUMA_INSTALL_PATH = "C:\uptime-kuma"
```

#### 2. 修改 Install-UptimeKuma 函數
- 移除本地變數 `$installPath`
- 使用全域常數 `$UPTIME_KUMA_INSTALL_PATH`

#### 3. 修改主程序 Main 函數
- 移除本地變數 `$uptimeKumaPath`
- 直接使用全域常數 `$UPTIME_KUMA_INSTALL_PATH`

#### 4. 修改 Show-InstallationSummary 函數
- 將硬編碼的路徑字串替換為全域常數

### 改進效果
1. **單一真實來源**：路徑只在一個地方定義
2. **易於維護**：修改路徑只需要修改一個地方
3. **降低錯誤風險**：避免因遺漏修改而導致的不一致問題
4. **提高可讀性**：代碼意圖更加清晰

### 最佳實踐
- 對於在多個地方使用的常數值，應該定義為全域常數
- 使用有意義的常數名稱，如 `$UPTIME_KUMA_INSTALL_PATH`
- 將常數定義放在腳本開頭，便於查找和修改
- 在註釋中說明常數的用途

### 未來建議
考慮將其他可能重複的配置項也定義為全域常數，例如：
- PM2 服務名稱
- 預設端口號
- 日誌檔案路徑
- 任務排程器任務名稱

這樣可以進一步提高腳本的可維護性和可配置性。
