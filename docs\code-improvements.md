# 代碼改進記錄

## 2025-07-23: 統一 Uptime Kuma 安裝路徑定義

### 問題描述
在 PowerShell 腳本 `Install-DevTools.ps1` 中，Uptime Kuma 的安裝路徑在多個地方重複定義：
1. `Install-UptimeKuma` 函數中的 `$installPath = "C:\uptime-kuma"`
2. 主程序 `Main` 函數中的 `$uptimeKumaPath = "C:\uptime-kuma"`
3. `Show-InstallationSummary` 函數中的硬編碼路徑

### 問題影響
- **維護困難**：需要修改路徑時必須記得同時修改多個地方
- **容易出錯**：可能遺漏某個地方的修改，導致路徑不一致
- **代碼重複**：違反 DRY (Don't Repeat Yourself) 原則

### 解決方案
將安裝路徑定義為全域常數，統一管理：

```powershell
# 全域常數定義
$UPTIME_KUMA_INSTALL_PATH = "C:\uptime-kuma"
```

### 修改內容

#### 1. 新增全域常數定義
在腳本開頭（第 17-18 行）新增：
```powershell
# 全域常數定義
$UPTIME_KUMA_INSTALL_PATH = "C:\uptime-kuma"
```

#### 2. 修改 Install-UptimeKuma 函數
- 移除本地變數 `$installPath`
- 使用全域常數 `$UPTIME_KUMA_INSTALL_PATH`

#### 3. 修改主程序 Main 函數
- 移除本地變數 `$uptimeKumaPath`
- 直接使用全域常數 `$UPTIME_KUMA_INSTALL_PATH`

#### 4. 修改 Show-InstallationSummary 函數
- 將硬編碼的路徑字串替換為全域常數

### 改進效果
1. **單一真實來源**：路徑只在一個地方定義
2. **易於維護**：修改路徑只需要修改一個地方
3. **降低錯誤風險**：避免因遺漏修改而導致的不一致問題
4. **提高可讀性**：代碼意圖更加清晰

### 最佳實踐
- 對於在多個地方使用的常數值，應該定義為全域常數
- 使用有意義的常數名稱，如 `$UPTIME_KUMA_INSTALL_PATH`
- 將常數定義放在腳本開頭，便於查找和修改
- 在註釋中說明常數的用途

#### 5. 移除函數參數依賴
- 移除 `Start-UptimeKumaService` 函數的 `param([string]$UptimeKumaPath)` 參數
- 移除 `Setup-PM2AutoStart` 函數的 `param([string]$UptimeKumaPath)` 參數
- 修改主程序中的函數調用，移除參數傳遞
- 所有函數直接使用全域常數 `$UPTIME_KUMA_INSTALL_PATH`

### 進一步改進（2025-07-23 更新）

#### 問題描述
在第一次改進後，發現仍有函數使用 `param([string]$UptimeKumaPath)` 參數來接收路徑，這造成了不必要的參數傳遞，違反了統一使用全域常數的原則。

#### 解決方案
完全移除所有函數的路徑參數，直接使用全域常數：

**修改前：**
```powershell
function Start-UptimeKumaService {
    param([string]$UptimeKumaPath)
    Set-Location $UptimeKumaPath
}

# 調用時需要傳遞參數
Start-UptimeKumaService -UptimeKumaPath $UPTIME_KUMA_INSTALL_PATH
```

**修改後：**
```powershell
function Start-UptimeKumaService {
    Set-Location $UPTIME_KUMA_INSTALL_PATH
}

# 調用時不需要參數
Start-UptimeKumaService
```

#### 改進效果
1. **簡化函數簽名**：移除不必要的參數
2. **簡化函數調用**：不需要傳遞參數
3. **完全統一**：所有地方都使用同一個全域常數
4. **減少錯誤風險**：避免參數傳遞錯誤

### 未來建議
考慮將其他可能重複的配置項也定義為全域常數，例如：
- PM2 服務名稱
- 預設端口號
- 日誌檔案路徑
- 任務排程器任務名稱

這樣可以進一步提高腳本的可維護性和可配置性。

## 2025-07-23: 腳本內容英文化

### 問題描述
原始腳本中的所有文字內容（註釋、輸出訊息、錯誤訊息等）都是繁體中文，這可能會在某些環境下造成編碼問題，也不利於國際化使用。

### 解決方案
將腳本中的所有中文內容改為英文，包括：

#### 1. 腳本標頭註釋
- `.SYNOPSIS`: 自動化安裝開發工具套件 → Automated Development Tools Installation Suite
- `.DESCRIPTION`: 自動安裝 Chocolatey, Git, Node.js, PM2 和 Uptime Kuma 監控工具 → Automatically installs Chocolatey, Git, Node.js, PM2 and Uptime Kuma monitoring tool

#### 2. 函數註釋
- 彩色輸出函數 → Colored output functions
- 檢查管理員權限 → Check administrator privileges
- 刷新環境變數 → Refresh environment variables
- 主要安裝函數 → Main installation functions

#### 3. 輸出訊息
- 正在安裝 Chocolatey... → Installing Chocolatey...
- 已安裝 → is already installed
- 安裝完成 → installation completed
- 安裝失敗 → installation failed

#### 4. 錯誤和警告訊息
- 所有錯誤訊息改為英文
- 警告訊息改為英文
- 狀態訊息改為英文

#### 5. 安裝摘要
- 安裝完成摘要 → Installation Summary
- 所有工具已成功安裝 → All tools have been successfully installed
- 常用 PM2 命令 → Common PM2 Commands
- 訪問信息 → Access Information

### 改進效果
1. **國際化支援**：英文內容更容易被國際用戶理解
2. **編碼相容性**：避免中文字符可能造成的編碼問題
3. **標準化**：符合國際軟體開發的標準做法
4. **可維護性**：英文註釋和訊息更容易維護和擴展

### 保持不變的內容
- 變數名稱和函數名稱保持原樣
- 程式邏輯完全不變
- 全域常數定義不變
- 功能性代碼不變

這次改進使腳本更加國際化，同時保持了所有原有功能。
