#Requires -Version 5.1

<#
.SYNOPSIS
    Automated Development Tools Installation Suite
.DESCRIPTION
    Automatically installs Chocolatey, Git, Node.js, PM2 and Uptime Kuma monitoring tool
.AUTHOR
    Development Team
.VERSION
    1.0.0
#>

# Set error handling
$ErrorActionPreference = "Stop"

# Global constants definition
$UPTIME_KUMA_INSTALL_PATH = "C:\uptime-kuma"

# Colored output functions
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

# Check administrator privileges
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Refresh environment variables
function Update-EnvironmentVariables {
    Write-Info "Refreshing environment variables..."
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
}

# Check if command exists
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Main installation functions
function Install-Chocolatey {
    Write-Info "Installing Chocolatey..."
    if (Test-Command "choco") {
        Write-Success "Chocolatey is already installed"
        return
    }

    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Update-EnvironmentVariables
        Write-Success "Chocolatey installation completed"
    }
    catch {
        Write-Error "Chocolatey installation failed: $($_.Exception.Message)"
        throw
    }
}

function Install-Git {
    Write-Info "Installing Git..."
    if (Test-Command "git") {
        Write-Success "Git is already installed"
        return
    }

    try {
        Write-Info "Installing Git via Chocolatey..."
        choco install git -y
        Update-EnvironmentVariables
        Write-Success "Git installation completed"
    }
    catch {
        Write-Error "Git installation failed: $($_.Exception.Message)"
        throw
    }
}

function Install-NodeJS {
    Write-Info "Installing Node.js..."
    if (Test-Command "node") {
        Write-Success "Node.js is already installed"
        return
    }

    try {
        Write-Info "Installing Node.js via Chocolatey..."
        choco install nodejs -y
        Update-EnvironmentVariables
        Write-Success "Node.js installation completed"
    }
    catch {
        Write-Error "Node.js installation failed: $($_.Exception.Message)"
        throw
    }
}

function Install-PM2 {
    Write-Info "Installing PM2..."
    if (Test-Command "pm2") {
        Write-Success "PM2 is already installed"
        return
    }

    try {
        Write-Info "Installing PM2 globally..."
        npm install -g pm2
        pm2 install pm2-logrotate
        Update-EnvironmentVariables
        Write-Success "PM2 installation completed"
    }
    catch {
        Write-Error "PM2 installation failed: $($_.Exception.Message)"
        throw
    }
}

function Install-UptimeKuma {
    Write-Info "Installing Uptime Kuma..."

    if (Test-Path $UPTIME_KUMA_INSTALL_PATH) {
        Write-Warning "Uptime Kuma directory already exists, skipping installation"
        return $UPTIME_KUMA_INSTALL_PATH
    }

    try {
        # Create installation directory
        New-Item -ItemType Directory -Path $UPTIME_KUMA_INSTALL_PATH -Force | Out-Null
        Set-Location $UPTIME_KUMA_INSTALL_PATH

        # Download and install specific version
        Write-Info "Downloading Uptime Kuma source code..."
        git clone https://github.com/louislam/uptime-kuma.git . | Out-Null
        git checkout 2.0.0-beta.2 | Out-Null

        # Install dependencies
        Write-Info "Installing Node.js dependencies (this may take a few minutes)..."
        npm ci --production | Out-Null
        Write-Info "Downloading pre-compiled resources..."
        npm run download-dist | Out-Null

        Write-Success "Uptime Kuma installation completed"
        return $UPTIME_KUMA_INSTALL_PATH
    }
    catch {
        Write-Error "Uptime Kuma installation failed: $($_.Exception.Message)"
        throw
    }
}

function Install-PM2LogRotate {
    Write-Info "Checking PM2 log rotation plugin..."

    try {
        # Set environment variable to ignore Node.js deprecation warnings
        $env:NODE_NO_WARNINGS = "1"

        # Check if plugin is already installed
        $existingPlugin = pm2 list | Select-String "pm2-logrotate"

        if ($existingPlugin) {
            Write-Success "PM2 log rotation plugin is already installed"
            # Restore environment variable
            $env:NODE_NO_WARNINGS = $null
            return
        }

        Write-Info "Installing PM2 log rotation plugin..."
        # Install PM2 log rotation plugin (officially recommended)
        pm2 install pm2-logrotate

        # Restore environment variable
        $env:NODE_NO_WARNINGS = $null

        # Check if plugin installation was successful
        Start-Sleep -Seconds 2
        $pluginStatus = pm2 list | Select-String "pm2-logrotate.*online"

        if ($pluginStatus) {
            Write-Success "PM2 log rotation plugin installation completed"
        } else {
            Write-Warning "PM2 log rotation plugin may not be properly installed, but it doesn't affect main functionality"
        }
    }
    catch {
        Write-Warning "PM2 log rotation plugin installation failed, but it doesn't affect main functionality"
        # Ensure environment variable is restored
        $env:NODE_NO_WARNINGS = $null
    }
}

function Start-UptimeKumaService {
    Write-Info "Starting Uptime Kuma service..."

    try {
        # Switch to Uptime Kuma directory
        Write-Info $UPTIME_KUMA_INSTALL_PATH
        Set-Location $UPTIME_KUMA_INSTALL_PATH
        # Stop existing uptime-kuma process (if exists)
        try {
            $anyStatus = pm2 list | Select-String "uptime-kuma"
            if ($anyStatus) {
                Write-Info "Attempting to stop existing uptime-kuma process..."
                pm2 delete uptime-kuma
            }
        }
        catch {
            Write-Info "No existing uptime-kuma process found, continuing installation..."
        }

        # Start service using officially recommended method
        Write-Info "Starting PM2 service..."

        # Start service and display output for debugging
        Write-Info "Executing PM2 start command..."
        pm2 start server/server.js --name uptime-kuma
        Write-Info "Saving PM2 configuration..."
        pm2 save

        # Verify if service started successfully
        Start-Sleep -Seconds 3
        $pm2Status = pm2 list | Select-String "uptime-kuma.*online"

        if ($pm2Status) {
            Write-Success "Uptime Kuma service has been started"
            Write-Success "Service status verification successful"
        } else {
            # Check if service exists in other status
            $anyStatus = pm2 list | Select-String "uptime-kuma"
            if ($anyStatus) {
                Write-Warning "Uptime Kuma service has been started, but status may not be online"
                Write-Info "Please check service status:"
                pm2 list
            } else {
                throw "Service startup failed, uptime-kuma process not found in PM2 list"
            }
        }
    }
    catch {
        Write-Error "Starting Uptime Kuma service failed: $($_.Exception.Message)"
        Write-Info "Attempting to view PM2 status..."
        pm2 list

        # Check if service is actually running
        $runningStatus = pm2 list | Select-String "uptime-kuma.*online"
        if ($runningStatus) {
            Write-Warning "Although an error occurred, the service seems to be running"
            Write-Success "You can try accessing http://localhost:3001"
            return  # Don't throw exception, continue execution
        } else {
            throw
        }
    }
}

function Setup-PM2AutoStart {
    Write-Info "Setting up PM2 auto-start on boot..."

    try {
        $taskName = "PM2-UptimeKuma-AutoStart"
        $batFileName = "pm2-autostart.bat"
        $batFilePath = Join-Path $UPTIME_KUMA_INSTALL_PATH $batFileName

        # Check if task already exists
        $existingTask = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($existingTask) {
            Write-Success "PM2 auto-start task already exists"
            return
        }

        # Create batch file
        Write-Info "Creating PM2 auto-start batch file..."
        $batContent = "pm2 resurrect"
        Set-Content -Path $batFilePath -Value $batContent -Encoding UTF8
        Write-Success "Batch file created: $batFilePath"

        # Create Windows scheduled task
        Write-Info "Creating Windows scheduled task..."

        # Define task action
        $action = New-ScheduledTaskAction -Execute "cmd" -Argument "/c start `"`" `"$batFilePath`""

        # Define trigger condition (at system startup)
        $trigger = New-ScheduledTaskTrigger -AtStartup

        # Define task settings
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

        # Define execution permissions (run as current user, regardless of login status)
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name

        # Prompt for user password to set up auto-start task
        Write-Info "Current user password is required to set up auto-start task..."
        $credential = Get-Credential -UserName $currentUser -Message "Please enter current user password to set up auto-start task"

        if (-not $credential) {
            Write-Warning "No credentials provided, skipping auto-start setup"
            return
        }

        $principal = New-ScheduledTaskPrincipal -UserId $credential.UserName -LogonType Password -RunLevel Highest

        # Create and register task
        $task = New-ScheduledTask -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description "PM2 Uptime Kuma Auto-Start Service"
        Register-ScheduledTask -TaskName $taskName -InputObject $task -User $credential.UserName -Password $credential.GetNetworkCredential().Password | Out-Null

        # Verify if task was created successfully
        $createdTask = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($createdTask) {
            Write-Success "PM2 auto-start task creation completed"
            Write-Info "Task name: $taskName"
            Write-Info "Batch file: $batFilePath"
        } else {
            Write-Warning "Task creation may have failed, please manually check Task Scheduler"
        }
    }
    catch {
        Write-Warning "Setting up PM2 auto-start failed: $($_.Exception.Message)"
        Write-Info "You can manually set up auto-start:"
        Write-Info "1. Open Task Scheduler (taskschd.msc)"
        Write-Info "2. Create basic task, trigger condition select 'When the computer starts'"
        Write-Info "3. Action select start program: cmd.exe"
        Write-Info "4. Arguments: /c start `"`" `"$batFilePath`""
    }
}

function Show-InstallationSummary {
    Write-ColorOutput ("`n" + "="*60) "Magenta"
    Write-ColorOutput "           Installation Summary" "Magenta"
    Write-ColorOutput ("="*60) "Magenta"

    Write-Success "All tools have been successfully installed:"
    Write-ColorOutput "  • Chocolatey - Windows Package Manager" "White"
    Write-ColorOutput "  • Git - Version Control System" "White"
    Write-ColorOutput "  • Node.js - JavaScript Runtime Environment" "White"
    Write-ColorOutput "  • PM2 - Process Manager" "White"
    Write-ColorOutput "  • PM2 Log Rotation Plugin - Automatic Log Management" "White"
    Write-ColorOutput "  • Uptime Kuma 2.0.0-beta.2 - Monitoring Tool" "White"
    Write-ColorOutput "  • Auto-Start Task - Automatically restore service after system restart" "White"

    Write-ColorOutput "`nCommon PM2 Commands:" "Yellow"
    Write-ColorOutput "  pm2 list                    # View all processes" "Gray"
    Write-ColorOutput "  pm2 logs uptime-kuma        # View logs" "Gray"
    Write-ColorOutput "  pm2 restart uptime-kuma     # Restart service" "Gray"
    Write-ColorOutput "  pm2 stop uptime-kuma        # Stop service" "Gray"
    Write-ColorOutput "  pm2 start uptime-kuma       # Start service" "Gray"
    Write-ColorOutput "  pm2 monit                   # Monitoring panel" "Gray"

    Write-ColorOutput "`nUptime Kuma Access Information:" "Yellow"
    Write-ColorOutput "  URL: http://localhost:3001" "Green"
    Write-ColorOutput "  Installation Path: $UPTIME_KUMA_INSTALL_PATH" "Gray"
    Write-ColorOutput "  PM2 Logs: pm2 logs uptime-kuma" "Gray"

    Write-ColorOutput "`nAuto-Start on Boot:" "Yellow"
    Write-ColorOutput "  Task Name: PM2-UptimeKuma-AutoStart" "Gray"
    Write-ColorOutput "  Batch File: $UPTIME_KUMA_INSTALL_PATH\pm2-autostart.bat" "Gray"
    Write-ColorOutput "  Management Tool: taskschd.msc (Task Scheduler)" "Gray"

    Write-ColorOutput "`nOpening browser..." "Cyan"
}

# Main program
function Main {
    try {
        # Check administrator privileges
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }

        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green

        # Set execution policy
        Set-ExecutionPolicy Bypass -Scope Process -Force
        Write-Success "Execution policy has been set"

        Write-ColorOutput "`nStarting automated installation process..." "Magenta"
        Write-ColorOutput ("="*50) "Magenta"

        # Install in sequence
        Install-Chocolatey
        Install-Git
        Install-NodeJS
        Install-PM2
        Install-UptimeKuma

        # Install PM2 plugins and start service
        #Install-PM2LogRotate
        Start-UptimeKumaService

        # Set up auto-start on boot
        Setup-PM2AutoStart

        # Wait for service to start
        Write-Info "Waiting for service to start..."
        Start-Sleep -Seconds 5

        # Show summary
        Show-InstallationSummary

        # Open browser
        Start-Process "http://localhost:3001"

        Write-ColorOutput "`nPress any key to exit..." "Yellow"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

    }
    catch {
        Write-Error "An error occurred during installation: $($_.Exception.Message)"
        Write-ColorOutput "`nPress any key to exit..." "Red"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit 1
    }
}

# Execute main program
Main