#Requires -Version 5.1

<#
.SYNOPSIS
    自動化安裝開發工具套件
.DESCRIPTION
    自動安裝 Chocolatey, Git, Node.js, PM2 和 Uptime Kuma 監控工具
.AUTHOR
    Development Team
.VERSION
    1.0.0
#>

# 設定錯誤處理
$ErrorActionPreference = "Stop"

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 刷新環境變數
function Update-EnvironmentVariables {
    Write-Info "刷新環境變數..."
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
}

# 檢查命令是否存在
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# 主要安裝函數
function Install-Chocolatey {
    Write-Info "正在安裝 Chocolatey..."
    if (Test-Command "choco") {
        Write-Success "Chocolatey 已安裝"
        return
    }
    
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Update-EnvironmentVariables
        Write-Success "Chocolatey 安裝完成"
    }
    catch {
        Write-Error "Chocolatey 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-Git {
    Write-Info "正在安裝 Git..."
    if (Test-Command "git") {
        Write-Success "Git 已安裝"
        return
    }
    
    try {
        Write-Info "正在通過 Chocolatey 安裝 Git..."
        choco install git -y
        Update-EnvironmentVariables
        Write-Success "Git 安裝完成"
    }
    catch {
        Write-Error "Git 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-NodeJS {
    Write-Info "正在安裝 Node.js..."
    if (Test-Command "node") {
        Write-Success "Node.js 已安裝"
        return
    }
    
    try {
        Write-Info "正在通過 Chocolatey 安裝 Node.js..."
        choco install nodejs -y
        Update-EnvironmentVariables
        Write-Success "Node.js 安裝完成"
    }
    catch {
        Write-Error "Node.js 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-PM2 {
    Write-Info "正在安裝 PM2..."
    if (Test-Command "pm2") {
        Write-Success "PM2 已安裝"
        return
    }
    
    try {
        Write-Info "正在全域安裝 PM2..."
        npm install -g pm2
        Update-EnvironmentVariables
        Write-Success "PM2 安裝完成"
    }
    catch {
        Write-Error "PM2 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-UptimeKuma {
    Write-Info "正在安裝 Uptime Kuma..."
    $installPath = "C:\uptime-kuma"
    
    if (Test-Path $installPath) {
        Write-Warning "Uptime Kuma 目錄已存在，跳過安裝"
        return $installPath
    }
    
    try {
        # 創建安裝目錄
        New-Item -ItemType Directory -Path $installPath -Force | Out-Null
        Set-Location $installPath
        
        # 下載並安裝特定版本
        Write-Info "正在下載 Uptime Kuma 源碼..."
        git clone https://github.com/louislam/uptime-kuma.git .
        git checkout 2.0.0-beta.2

        # 安裝依賴
        Write-Info "正在安裝 Node.js 依賴（這可能需要幾分鐘）..."
        npm ci --production
        Write-Info "正在下載預編譯資源..."
        npm run download-dist
        
        Write-Success "Uptime Kuma 安裝完成"
        return $installPath
    }
    catch {
        Write-Error "Uptime Kuma 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-PM2LogRotate {
    Write-Info "檢查 PM2 日誌輪轉插件..."

    try {
        # 設定環境變數以忽略 Node.js 棄用警告
        $env:NODE_NO_WARNINGS = "1"

        # 檢查插件是否已經安裝
        $existingPlugin = pm2 list | Select-String "pm2-logrotate"

        if ($existingPlugin) {
            Write-Success "PM2 日誌輪轉插件已安裝"
            # 恢復環境變數
            $env:NODE_NO_WARNINGS = $null
            return
        }

        Write-Info "安裝 PM2 日誌輪轉插件..."
        # 安裝 PM2 日誌輪轉插件（官方推薦）
        pm2 install pm2-logrotate

        # 恢復環境變數
        $env:NODE_NO_WARNINGS = $null

        # 檢查插件是否安裝成功
        Start-Sleep -Seconds 2
        $pluginStatus = pm2 list | Select-String "pm2-logrotate.*online"

        if ($pluginStatus) {
            Write-Success "PM2 日誌輪轉插件安裝完成"
        } else {
            Write-Warning "PM2 日誌輪轉插件可能未正確安裝，但不影響主要功能"
        }
    }
    catch {
        Write-Warning "PM2 日誌輪轉插件安裝失敗，但不影響主要功能"
        # 確保環境變數被恢復
        $env:NODE_NO_WARNINGS = $null
    }
}

function Start-UptimeKumaService {
    param([string]$UptimeKumaPath)

    Write-Info "啟動 Uptime Kuma 服務..."

    try {
        # 切換到 Uptime Kuma 目錄
        Set-Location $UptimeKumaPath

        # 停止現有的 uptime-kuma 進程（如果存在）
        try {
            Write-Info "嘗試停止現有的 uptime-kuma 進程..."
            pm2 delete uptime-kuma
        }
        catch {
            Write-Info "沒有找到現有的 uptime-kuma 進程，繼續安裝..."
        }

        # 使用官方推薦的方式啟動服務
        Write-Info "正在啟動 PM2 服務..."

        # 啟動服務並顯示輸出以便調試
        Write-Info "執行 PM2 啟動命令..."
        pm2 start server/server.js --name uptime-kuma
        Write-Info "保存 PM2 配置..."
        pm2 save

        # 驗證服務是否正常啟動
        Start-Sleep -Seconds 3
        $pm2Status = pm2 list | Select-String "uptime-kuma.*online"

        if ($pm2Status) {
            Write-Success "Uptime Kuma 服務已啟動"
            Write-Success "服務狀態驗證成功"
        } else {
            # 檢查服務是否以其他狀態存在
            $anyStatus = pm2 list | Select-String "uptime-kuma"
            if ($anyStatus) {
                Write-Warning "Uptime Kuma 服務已啟動，但狀態可能不是 online"
                Write-Info "請檢查服務狀態："
                pm2 list
            } else {
                throw "服務啟動失敗，PM2 列表中找不到 uptime-kuma 進程"
            }
        }
    }
    catch {
        Write-Error "啟動 Uptime Kuma 服務失敗: $($_.Exception.Message)"
        Write-Info "嘗試查看 PM2 狀態..."
        pm2 list

        # 檢查服務是否實際上已經在運行
        $runningStatus = pm2 list | Select-String "uptime-kuma.*online"
        if ($runningStatus) {
            Write-Warning "雖然出現錯誤，但服務似乎已經在運行中"
            Write-Success "可以嘗試訪問 http://localhost:3001"
            return  # 不拋出異常，繼續執行
        } else {
            throw
        }
    }
}

function Setup-PM2AutoStart {
    param([string]$UptimeKumaPath)

    Write-Info "設定 PM2 開機自動啟動..."

    try {
        $taskName = "PM2-UptimeKuma-AutoStart"
        $batFileName = "pm2-autostart.bat"
        $batFilePath = Join-Path $UptimeKumaPath $batFileName

        # 檢查任務是否已存在
        $existingTask = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($existingTask) {
            Write-Success "PM2 開機自動啟動任務已存在"
            return
        }

        # 創建批次檔案
        Write-Info "創建 PM2 自動啟動批次檔案..."
        $batContent = @"
@echo off
cd /d "$UptimeKumaPath"
pm2 resurrect
"@
        Set-Content -Path $batFilePath -Value $batContent -Encoding UTF8
        Write-Success "批次檔案已創建: $batFilePath"

        # 創建工作排程任務
        Write-Info "創建 Windows 工作排程任務..."

        # 定義任務動作
        $action = New-ScheduledTaskAction -Execute "cmd.exe" -Argument "/c start `"`" `"$batFilePath`""

        # 定義觸發條件（系統啟動時）
        $trigger = New-ScheduledTaskTrigger -AtStartup

        # 定義任務設定
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

        # 定義執行權限（以當前用戶身份執行，不論是否登入）
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name

        # 彈出認證對話框讓用戶輸入密碼
        Write-Info "需要輸入當前用戶的密碼以設定開機自動啟動任務..."
        $credential = Get-Credential -UserName $currentUser -Message "請輸入當前用戶的密碼以設定開機自動啟動任務"

        if (-not $credential) {
            Write-Warning "未提供認證信息，跳過開機自動啟動設定"
            return
        }

        $principal = New-ScheduledTaskPrincipal -UserId $credential.UserName -LogonType Password -RunLevel Highest

        # 創建並註冊任務
        $task = New-ScheduledTask -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description "PM2 Uptime Kuma 開機自動啟動服務"
        Register-ScheduledTask -TaskName $taskName -InputObject $task -User $credential.UserName -Password $credential.GetNetworkCredential().Password | Out-Null

        # 驗證任務是否創建成功
        $createdTask = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($createdTask) {
            Write-Success "PM2 開機自動啟動任務創建完成"
            Write-Info "任務名稱: $taskName"
            Write-Info "批次檔案: $batFilePath"
        } else {
            Write-Warning "任務創建可能失敗，請手動檢查工作排程器"
        }
    }
    catch {
        Write-Warning "設定 PM2 開機自動啟動失敗: $($_.Exception.Message)"
        Write-Info "您可以手動設定開機啟動："
        Write-Info "1. 打開工作排程器 (taskschd.msc)"
        Write-Info "2. 創建基本任務，觸發條件選擇 '電腦啟動時'"
        Write-Info "3. 動作選擇啟動程式: cmd.exe"
        Write-Info "4. 參數填入: /c start `"`" `"$batFilePath`""
    }
}

function Show-InstallationSummary {
    Write-ColorOutput ("`n" + "="*60) "Magenta"
    Write-ColorOutput "           安裝完成摘要" "Magenta"
    Write-ColorOutput ("="*60) "Magenta"
    
    Write-Success "所有工具已成功安裝："
    Write-ColorOutput "  • Chocolatey - Windows 套件管理器" "White"
    Write-ColorOutput "  • Git - 版本控制系統" "White"
    Write-ColorOutput "  • Node.js - JavaScript 運行環境" "White"
    Write-ColorOutput "  • PM2 - 進程管理器" "White"
    Write-ColorOutput "  • PM2 日誌輪轉插件 - 自動日誌管理" "White"
    Write-ColorOutput "  • Uptime Kuma 2.0.0-beta.2 - 監控工具" "White"
    Write-ColorOutput "  • 開機自動啟動任務 - 系統重啟後自動恢復服務" "White"
    
    Write-ColorOutput "`n常用 PM2 命令：" "Yellow"
    Write-ColorOutput "  pm2 list                    # 查看所有進程" "Gray"
    Write-ColorOutput "  pm2 logs uptime-kuma        # 查看日誌" "Gray"
    Write-ColorOutput "  pm2 restart uptime-kuma     # 重啟服務" "Gray"
    Write-ColorOutput "  pm2 stop uptime-kuma        # 停止服務" "Gray"
    Write-ColorOutput "  pm2 start uptime-kuma       # 啟動服務" "Gray"
    Write-ColorOutput "  pm2 monit                   # 監控面板" "Gray"
    
    Write-ColorOutput "`nUptime Kuma 訪問信息：" "Yellow"
    Write-ColorOutput "  URL: http://localhost:3001" "Green"
    Write-ColorOutput "  安裝路徑: C:\uptime-kuma" "Gray"
    Write-ColorOutput "  PM2 日誌: pm2 logs uptime-kuma" "Gray"

    Write-ColorOutput "`n開機自動啟動：" "Yellow"
    Write-ColorOutput "  任務名稱: PM2-UptimeKuma-AutoStart" "Gray"
    Write-ColorOutput "  批次檔案: C:\uptime-kuma\pm2-autostart.bat" "Gray"
    Write-ColorOutput "  管理工具: taskschd.msc (工作排程器)" "Gray"

    Write-ColorOutput "`n正在開啟瀏覽器..." "Cyan"
}

# 主程序
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }

        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green

        # 設定執行策略
        Set-ExecutionPolicy Bypass -Scope Process -Force
        Write-Success "執行策略已設定"
        
        Write-ColorOutput "`n開始自動化安裝程序..." "Magenta"
        Write-ColorOutput ("="*50) "Magenta"
        
        # 按順序安裝
        Install-Chocolatey
        Install-Git
        Install-NodeJS
        Install-PM2
        $uptimeKumaPath = Install-UptimeKuma

        # 安裝 PM2 插件和啟動服務
        Install-PM2LogRotate
        Start-UptimeKumaService -UptimeKumaPath $uptimeKumaPath

        # 設定開機自動啟動
        Setup-PM2AutoStart -UptimeKumaPath $uptimeKumaPath

        # 等待服務啟動
        Write-Info "等待服務啟動..."
        Start-Sleep -Seconds 5
        
        # 顯示摘要
        Show-InstallationSummary
        
        # 開啟瀏覽器
        Start-Process "http://localhost:3001"
        
        Write-ColorOutput "`n按任意鍵退出..." "Yellow"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
    }
    catch {
        Write-Error "安裝過程中發生錯誤: $($_.Exception.Message)"
        Write-ColorOutput "`n按任意鍵退出..." "Red"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit 1
    }
}

# 執行主程序
Main