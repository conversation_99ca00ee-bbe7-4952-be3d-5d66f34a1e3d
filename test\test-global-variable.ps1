# 測試全域變數定義
# 這個腳本用來驗證 Install-DevTools.ps1 中的全域變數是否正確定義

# 載入主腳本的全域變數定義部分
$UPTIME_KUMA_INSTALL_PATH = "C:\uptime-kuma"

Write-Host "開始測試全域變數..." -ForegroundColor Yellow
Write-Host ("=" * 50) -ForegroundColor Yellow

# 測試變數是否已定義
Write-Host "測試全域變數定義..." -ForegroundColor Cyan
if ($null -eq $UPTIME_KUMA_INSTALL_PATH) {
    Write-Host "錯誤：全域變數 UPTIME_KUMA_INSTALL_PATH 未定義" -ForegroundColor Red
    exit 1
}

# 測試變數值是否正確
if ($UPTIME_KUMA_INSTALL_PATH -eq "C:\uptime-kuma") {
    Write-Host "成功：全域變數 UPTIME_KUMA_INSTALL_PATH = '$UPTIME_KUMA_INSTALL_PATH'" -ForegroundColor Green
} else {
    Write-Host "錯誤：全域變數值不正確，期望 'C:\uptime-kuma'，實際 '$UPTIME_KUMA_INSTALL_PATH'" -ForegroundColor Red
    exit 1
}

# 測試路徑使用
Write-Host "測試路徑使用..." -ForegroundColor Cyan
$testPath = $UPTIME_KUMA_INSTALL_PATH

if ($testPath -eq "C:\uptime-kuma") {
    Write-Host "成功：函數中可以正確使用全域變數" -ForegroundColor Green
} else {
    Write-Host "錯誤：函數中無法正確使用全域變數" -ForegroundColor Red
    exit 1
}

Write-Host ("=" * 50) -ForegroundColor Yellow
Write-Host "所有測試通過！全域變數定義正確。" -ForegroundColor Green
Write-Host "測試完成。" -ForegroundColor Cyan
