# Simple test for global variable
$UPTIME_KUMA_INSTALL_PATH = "C:\uptime-kuma"

Write-Host "Testing global variable..." -ForegroundColor Yellow

if ($UPTIME_KUMA_INSTALL_PATH -eq "C:\uptime-kuma") {
    Write-Host "SUCCESS: Global variable is correctly defined" -ForegroundColor Green
    Write-Host "Value: $UPTIME_KUMA_INSTALL_PATH" -ForegroundColor Cyan
} else {
    Write-Host "ERROR: Global variable value is incorrect" -ForegroundColor Red
    exit 1
}

Write-Host "Test completed successfully!" -ForegroundColor Green
