# Test function calls without parameters
# This script tests that functions can be called without parameters and use global variable

# Define global variable (same as in main script)
$UPTIME_KUMA_INSTALL_PATH = "C:\uptime-kuma"

# Mock functions to test parameter removal
function Test-Start-UptimeKumaService {
    Write-Host "Testing Start-UptimeKumaService function..." -ForegroundColor Cyan
    
    # Simulate the function logic
    if ($null -ne $UPTIME_KUMA_INSTALL_PATH) {
        Write-Host "SUCCESS: Function can access global variable: $UPTIME_KUMA_INSTALL_PATH" -ForegroundColor Green
        return $true
    } else {
        Write-Host "ERROR: Function cannot access global variable" -ForegroundColor Red
        return $false
    }
}

function Test-Setup-PM2AutoStart {
    Write-Host "Testing Setup-PM2AutoStart function..." -ForegroundColor Cyan
    
    # Simulate the function logic
    $batFilePath = Join-Path $UPTIME_KUMA_INSTALL_PATH "pm2-autostart.bat"
    
    if ($batFilePath -eq "C:\uptime-kuma\pm2-autostart.bat") {
        Write-Host "SUCCESS: Function can create correct path: $batFilePath" -ForegroundColor Green
        return $true
    } else {
        Write-Host "ERROR: Function created incorrect path: $batFilePath" -ForegroundColor Red
        return $false
    }
}

# Run tests
Write-Host "Testing function calls without parameters..." -ForegroundColor Yellow
Write-Host ("=" * 50) -ForegroundColor Yellow

$test1 = Test-Start-UptimeKumaService
$test2 = Test-Setup-PM2AutoStart

Write-Host ("=" * 50) -ForegroundColor Yellow

if ($test1 -and $test2) {
    Write-Host "SUCCESS: All function tests passed!" -ForegroundColor Green
    Write-Host "Functions can be called without parameters and access global variable correctly." -ForegroundColor Green
} else {
    Write-Host "ERROR: Some function tests failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Test completed successfully!" -ForegroundColor Cyan
